package org.springblade.modules.beachwaste.serialization;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.springblade.modules.beachwaste.config.JacksonGeometryConfig;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.vo.EventDetailVO;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Event序列化测试类
 * 验证Event实体和EventDetailVO的Jackson序列化功能
 */
class EventSerializationTest {

    private ObjectMapper objectMapper;
    private GeometryFactory geometryFactory;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        
        // 应用几何对象序列化配置
        JacksonGeometryConfig config = new JacksonGeometryConfig();
        config.jacksonGeometryCustomizer().customize(objectMapper.getFactory().getBuilder());
        
        geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);
    }

    @Test
    void testEventSerialization() throws Exception {
        // 创建测试Event对象
        Event event = new Event();
        event.setId(1L);
        event.setWasteMaterial(1L);
        event.setWasteSize(2L);
        event.setDiscoveryMethod(0L);
        event.setConfidence(BigDecimal.valueOf(0.85));
        event.setDiscoveryTime(new Date());
        
        // 设置Point位置
        Point location = geometryFactory.createPoint(new Coordinate(120.123456, 30.654321));
        event.setLocation(location);

        // 测试序列化
        String json = objectMapper.writeValueAsString(event);
        
        // 验证序列化结果
        assertNotNull(json);
        assertFalse(json.isEmpty());
        
        // 验证不包含location字段（因为@JsonIgnore）
        assertFalse(json.contains("\"location\""));
        
        // 验证包含locationInfo字段
        assertTrue(json.contains("\"locationInfo\""));
        assertTrue(json.contains("\"longitude\":120.123456"));
        assertTrue(json.contains("\"latitude\":30.654321"));
        
        System.out.println("Event序列化结果: " + json);
    }

    @Test
    void testEventDetailVOSerialization() throws Exception {
        // 创建测试EventDetailVO对象
        EventDetailVO vo = new EventDetailVO();
        vo.setId(1L);
        vo.setEventTitle("Test Event");
        vo.setWasteSizeDesc("Small");
        vo.setWasteMaterialDesc("Plastic");
        vo.setGridName("Grid A");
        vo.setDiscoveryMethodDesc("AI Detection");
        vo.setConfidence(BigDecimal.valueOf(0.85));
        vo.setEventStatus(1L);
        vo.setDiscoveryTime(new Date());
        
        // 设置经纬度字符串
        vo.setLongitude("120.123456");
        vo.setLatitude("30.654321");

        // 测试序列化
        String json = objectMapper.writeValueAsString(vo);
        
        // 验证序列化结果
        assertNotNull(json);
        assertFalse(json.isEmpty());
        
        // 验证包含经纬度字段
        assertTrue(json.contains("\"longitude\":\"120.123456\""));
        assertTrue(json.contains("\"latitude\":\"30.654321\""));
        
        System.out.println("EventDetailVO序列化结果: " + json);
    }

    @Test
    void testEventWithNullLocation() throws Exception {
        // 创建没有位置信息的Event对象
        Event event = new Event();
        event.setId(2L);
        event.setWasteMaterial(1L);
        event.setLocation(null);

        // 测试序列化
        String json = objectMapper.writeValueAsString(event);
        
        // 验证序列化结果
        assertNotNull(json);
        assertTrue(json.contains("\"locationInfo\":null"));
        
        System.out.println("无位置Event序列化结果: " + json);
    }

    @Test
    void testEventWithEmptyLocation() throws Exception {
        // 创建空位置的Event对象
        Event event = new Event();
        event.setId(3L);
        event.setWasteMaterial(1L);
        
        // 创建空的Point对象
        Point emptyLocation = geometryFactory.createPoint();
        event.setLocation(emptyLocation);

        // 测试序列化
        String json = objectMapper.writeValueAsString(event);
        
        // 验证序列化结果
        assertNotNull(json);
        assertTrue(json.contains("\"locationInfo\":null"));
        
        System.out.println("空位置Event序列化结果: " + json);
    }

    @Test
    void testSerializationPerformance() throws Exception {
        // 性能测试：序列化大量Event对象
        int testCount = 1000;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            Event event = new Event();
            event.setId((long) i);
            event.setWasteMaterial(1L);
            
            Point location = geometryFactory.createPoint(new Coordinate(120.0 + i * 0.001, 30.0 + i * 0.001));
            event.setLocation(location);
            
            String json = objectMapper.writeValueAsString(event);
            assertNotNull(json);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("序列化" + testCount + "个Event对象耗时: " + duration + "ms");
        assertTrue(duration < 5000, "序列化性能应该在5秒内完成");
    }
}
