package org.springblade.modules.beachwaste.typehandler;

import org.apache.ibatis.type.JdbcType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.postgresql.util.PGobject;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * PointTypeHandler 测试类
 * 验证JTS Point与PostGIS几何类型之间的转换功能
 */
class PointTypeHandlerTest {

    private PointTypeHandler pointTypeHandler;
    private GeometryFactory geometryFactory;

    @Mock
    private PreparedStatement preparedStatement;

    @Mock
    private ResultSet resultSet;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        pointTypeHandler = new PointTypeHandler();
        // 使用WGS84坐标系 (SRID 4326)
        geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);
    }

    @Test
    void testSetNonNullParameter() throws SQLException {
        // Create test Point
        Point testPoint = geometryFactory.createPoint(new Coordinate(120.123456, 30.654321));

        // Execute parameter setting
        assertDoesNotThrow(() -> {
            pointTypeHandler.setNonNullParameter(preparedStatement, 1, testPoint, JdbcType.OTHER);
        });

        // Verify PreparedStatement.setObject was called with PGobject
        verify(preparedStatement, times(1)).setObject(eq(1), any(PGobject.class));
    }

    @Test
    void testGetNullableResultWithColumnName() throws SQLException {
        // Mock database returning null
        when(resultSet.getObject("location")).thenReturn(null);

        Point result = pointTypeHandler.getNullableResult(resultSet, "location");
        assertNull(result);

        // Mock database returning PGobject with WKT
        PGobject pgObject = new PGobject();
        pgObject.setType("geometry");
        pgObject.setValue("POINT(120.123456 30.654321)");
        when(resultSet.getObject("location")).thenReturn(pgObject);

        result = pointTypeHandler.getNullableResult(resultSet, "location");
        assertNotNull(result);
        assertEquals(120.123456, result.getX(), 0.000001);
        assertEquals(30.654321, result.getY(), 0.000001);
    }

    @Test
    void testGetNullableResultWithWKTString() throws SQLException {
        // Mock database returning WKT string
        String wktString = "POINT(120.123456 30.654321)";
        when(resultSet.getObject("location")).thenReturn(wktString);

        Point result = pointTypeHandler.getNullableResult(resultSet, "location");
        assertNotNull(result);
        assertEquals(120.123456, result.getX(), 0.000001);
        assertEquals(30.654321, result.getY(), 0.000001);
    }

    @Test
    void testGetNullableResultWithInvalidType() throws SQLException {
        // Mock database returning unsupported type
        when(resultSet.getObject("location")).thenReturn(123);

        SQLException exception = assertThrows(SQLException.class, () -> {
            pointTypeHandler.getNullableResult(resultSet, "location");
        });

        assertTrue(exception.getMessage().contains("Unsupported database object type"));
    }

    @Test
    void testGetNullableResultWithInvalidWKT() throws SQLException {
        // Mock database returning invalid WKT string
        when(resultSet.getObject("location")).thenReturn("INVALID WKT");

        SQLException exception = assertThrows(SQLException.class, () -> {
            pointTypeHandler.getNullableResult(resultSet, "location");
        });

        assertTrue(exception.getMessage().contains("Failed to parse WKT string"));
    }

    @Test
    void testGetNullableResultWithColumnIndex() throws SQLException {
        when(resultSet.getObject(1)).thenReturn(null);

        Point result = pointTypeHandler.getNullableResult(resultSet, 1);
        assertNull(result);
    }
}
