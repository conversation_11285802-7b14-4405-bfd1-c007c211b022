package org.springblade.modules.beachwaste.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.vo.EventDetailVO;
import org.springblade.modules.beachwaste.util.LocationSerializationUtil;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EventDetailVO测试类
 * 验证修复后的序列化功能和位置字段处理
 */
class EventDetailVOTest {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final GeometryFactory geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);

    @Test
    void testEventDetailVOSerialization() throws Exception {
        // 创建测试EventDetailVO对象
        EventDetailVO vo = new EventDetailVO();
        vo.setId(1L);
        vo.setEventTitle("Test Event");
        vo.setWasteSizeDesc("Small");
        vo.setWasteMaterialDesc("Plastic");
        vo.setGridName("Grid A");
        vo.setDiscoveryMethodDesc("AI Detection");
        vo.setConfidence(BigDecimal.valueOf(0.85));
        vo.setEventStatus(1L);
        vo.setDiscoveryTime(new Date());
        
        // 设置位置字符串（经度,纬度格式）
        vo.setLocation("120.123456,30.654321");

        // 测试序列化
        String json = objectMapper.writeValueAsString(vo);
        
        // 验证序列化结果
        assertNotNull(json);
        assertFalse(json.isEmpty());
        
        // 验证包含位置字段
        assertTrue(json.contains("\"location\":\"120.123456,30.654321\""));
        
        System.out.println("EventDetailVO序列化结果: " + json);
    }

    @Test
    void testEventSerialization() throws Exception {
        // 创建测试Event对象
        Event event = new Event();
        event.setId(1L);
        event.setWasteMaterial(1L);
        event.setWasteSize(2L);
        event.setDiscoveryMethod(0L);
        event.setConfidence(BigDecimal.valueOf(0.85));
        event.setDiscoveryTime(new Date());
        
        // 设置Point位置
        Point location = geometryFactory.createPoint(new Coordinate(120.123456, 30.654321));
        event.setLocation(location);

        // 测试序列化
        String json = objectMapper.writeValueAsString(event);
        
        // 验证序列化结果
        assertNotNull(json);
        assertFalse(json.isEmpty());
        
        // 验证不包含location字段（因为@JsonIgnore）
        assertFalse(json.contains("\"location\""));
        
        // 验证包含locationInfo字段
        assertTrue(json.contains("\"locationInfo\""));
        assertTrue(json.contains("\"longitude\":120.123456"));
        assertTrue(json.contains("\"latitude\":30.654321"));
        
        System.out.println("Event序列化结果: " + json);
    }

    @Test
    void testPointToStringConversion() {
        // 测试Point对象到字符串的转换
        Point testPoint = geometryFactory.createPoint(new Coordinate(120.123456, 30.654321));
        
        String locationStr = LocationSerializationUtil.pointToString(testPoint);
        assertEquals("120.123456,30.654321", locationStr);
        
        // 测试null Point
        String nullLocationStr = LocationSerializationUtil.pointToString(null);
        assertEquals("0.0,0.0", nullLocationStr);
        
        // 测试空Point
        Point emptyPoint = geometryFactory.createPoint();
        String emptyLocationStr = LocationSerializationUtil.pointToString(emptyPoint);
        assertEquals("0.0,0.0", emptyLocationStr);
        
        System.out.println("Point转换测试通过");
    }

    @Test
    void testEventDetailVOWithNullLocation() throws Exception {
        // 创建没有位置信息的EventDetailVO对象
        EventDetailVO vo = new EventDetailVO();
        vo.setId(2L);
        vo.setEventTitle("Test Event Without Location");
        vo.setLocation(null);

        // 测试序列化
        String json = objectMapper.writeValueAsString(vo);
        
        // 验证序列化结果
        assertNotNull(json);
        assertTrue(json.contains("\"location\":null"));
        
        System.out.println("无位置EventDetailVO序列化结果: " + json);
    }

    @Test
    void testEventDetailVOWithEmptyLocation() throws Exception {
        // 创建空位置的EventDetailVO对象
        EventDetailVO vo = new EventDetailVO();
        vo.setId(3L);
        vo.setEventTitle("Test Event With Empty Location");
        vo.setLocation("");

        // 测试序列化
        String json = objectMapper.writeValueAsString(vo);
        
        // 验证序列化结果
        assertNotNull(json);
        assertTrue(json.contains("\"location\":\"\""));
        
        System.out.println("空位置EventDetailVO序列化结果: " + json);
    }

    @Test
    void testLocationStringFormat() {
        // 测试各种位置字符串格式
        String[] testLocations = {
            "120.123456,30.654321",
            "0.0,0.0",
            "-120.123456,-30.654321",
            "180.0,90.0",
            "-180.0,-90.0"
        };
        
        for (String location : testLocations) {
            EventDetailVO vo = new EventDetailVO();
            vo.setId(1L);
            vo.setLocation(location);
            
            try {
                String json = objectMapper.writeValueAsString(vo);
                assertTrue(json.contains("\"location\":\"" + location + "\""));
                System.out.println("位置格式 " + location + " 序列化成功");
            } catch (Exception e) {
                fail("位置格式 " + location + " 序列化失败: " + e.getMessage());
            }
        }
    }
}
