package org.springblade.modules.beachwaste.service.impl;

import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.vo.EventLocationVO;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EventServiceImpl测试类
 * 验证Point对象处理功能
 */
class EventServiceImplTest {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory(new PrecisionModel(), 4326);

    /**
     * 模拟EventServiceImpl中的convertToEventLocationVO方法
     */
    private EventLocationVO convertToEventLocationVO(Event event) {
        if (event == null || event.getLocation() == null || event.getLocation().isEmpty()) {
            return null;
        }

        EventLocationVO vo = new EventLocationVO();
        vo.setId(event.getId());

        try {
            Point location = event.getLocation();
            double longitude = location.getX();
            double latitude = location.getY();
            
            vo.setLongitude(String.valueOf(longitude));
            vo.setLatitude(String.valueOf(latitude));
            
        } catch (Exception e) {
            System.err.println("解析事件位置信息失败，事件ID: " + event.getId() + ", 错误: " + e.getMessage());
            return null;
        }

        return vo;
    }

    /**
     * 模拟EventServiceImpl中的convertPointToLocationString方法
     */
    private String convertPointToLocationString(Point point) {
        if (point == null || point.isEmpty()) {
            return "0.0,0.0";
        }
        
        try {
            double longitude = point.getX();
            double latitude = point.getY();
            return longitude + "," + latitude;
        } catch (Exception e) {
            System.err.println("转换Point对象为位置字符串失败: " + e.getMessage());
            return "0.0,0.0";
        }
    }

    @Test
    void testConvertToEventLocationVOWithValidPoint() {
        // 创建测试数据
        Point testPoint = GEOMETRY_FACTORY.createPoint(new Coordinate(120.123456, 30.654321));
        Event event = new Event();
        event.setId(1L);
        event.setLocation(testPoint);
        
        // 执行测试
        EventLocationVO vo = convertToEventLocationVO(event);
        
        // 验证结果
        assertNotNull(vo);
        assertEquals(1L, vo.getId());
        assertEquals("120.123456", vo.getLongitude());
        assertEquals("30.654321", vo.getLatitude());
    }

    @Test
    void testConvertToEventLocationVOWithNullEvent() {
        EventLocationVO vo = convertToEventLocationVO(null);
        assertNull(vo);
    }

    @Test
    void testConvertToEventLocationVOWithNullLocation() {
        Event event = new Event();
        event.setId(1L);
        event.setLocation(null);
        
        EventLocationVO vo = convertToEventLocationVO(event);
        assertNull(vo);
    }

    @Test
    void testConvertPointToLocationStringWithValidPoint() {
        Point testPoint = GEOMETRY_FACTORY.createPoint(new Coordinate(120.123456, 30.654321));
        
        String locationStr = convertPointToLocationString(testPoint);
        
        assertEquals("120.123456,30.654321", locationStr);
    }

    @Test
    void testConvertPointToLocationStringWithNullPoint() {
        String locationStr = convertPointToLocationString(null);
        assertEquals("0.0,0.0", locationStr);
    }

    @Test
    void testConvertPointToLocationStringWithEmptyPoint() {
        Point emptyPoint = GEOMETRY_FACTORY.createPoint();
        
        String locationStr = convertPointToLocationString(emptyPoint);
        assertEquals("0.0,0.0", locationStr);
    }

    @Test
    void testConvertPointToLocationStringWithZeroCoordinates() {
        Point zeroPoint = GEOMETRY_FACTORY.createPoint(new Coordinate(0.0, 0.0));
        
        String locationStr = convertPointToLocationString(zeroPoint);
        assertEquals("0.0,0.0", locationStr);
    }

    @Test
    void testConvertPointToLocationStringWithNegativeCoordinates() {
        Point negativePoint = GEOMETRY_FACTORY.createPoint(new Coordinate(-120.123456, -30.654321));
        
        String locationStr = convertPointToLocationString(negativePoint);
        assertEquals("-120.123456,-30.654321", locationStr);
    }
}
