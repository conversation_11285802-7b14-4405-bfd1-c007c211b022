package org.springblade.modules.beachwaste.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.vo.EventDetailVO;
import org.springblade.modules.beachwaste.util.LocationSerializationUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Event序列化集成测试
 * 验证修复后的序列化功能在实际场景中的表现
 */
class EventSerializationIntegrationTest {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final GeometryFactory geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);

    @Test
    void testEventListSerialization() throws Exception {
        // 创建多个Event对象的列表，模拟API返回的数据
        List<Event> eventList = new ArrayList<>();
        
        for (int i = 0; i < 10; i++) {
            Event event = createTestEvent(i);
            eventList.add(event);
        }

        // 测试序列化整个列表
        String json = objectMapper.writeValueAsString(eventList);
        
        // 验证序列化成功
        assertNotNull(json);
        assertFalse(json.isEmpty());
        assertTrue(json.startsWith("["));
        assertTrue(json.endsWith("]"));
        
        // 验证包含locationInfo而不是location
        assertTrue(json.contains("\"locationInfo\""));
        assertFalse(json.contains("\"location\":{"));
        
        System.out.println("Event列表序列化成功，长度: " + json.length());
    }

    @Test
    void testEventDetailVOListSerialization() throws Exception {
        // 创建多个EventDetailVO对象的列表
        List<EventDetailVO> voList = new ArrayList<>();
        
        for (int i = 0; i < 10; i++) {
            EventDetailVO vo = createTestEventDetailVO(i);
            voList.add(vo);
        }

        // 测试序列化整个列表
        String json = objectMapper.writeValueAsString(voList);
        
        // 验证序列化成功
        assertNotNull(json);
        assertFalse(json.isEmpty());
        assertTrue(json.startsWith("["));
        assertTrue(json.endsWith("]"));
        
        // 验证包含经纬度字段
        assertTrue(json.contains("\"longitude\""));
        assertTrue(json.contains("\"latitude\""));
        
        System.out.println("EventDetailVO列表序列化成功，长度: " + json.length());
    }

    @Test
    void testMixedDataSerialization() throws Exception {
        // 创建包含Event和EventDetailVO的复杂数据结构
        class ApiResponse {
            public List<Event> events;
            public List<EventDetailVO> eventDetails;
            public String message;
            public int total;
        }
        
        ApiResponse response = new ApiResponse();
        response.events = new ArrayList<>();
        response.eventDetails = new ArrayList<>();
        response.message = "Success";
        response.total = 5;
        
        for (int i = 0; i < 5; i++) {
            response.events.add(createTestEvent(i));
            response.eventDetails.add(createTestEventDetailVO(i));
        }

        // 测试序列化复杂对象
        String json = objectMapper.writeValueAsString(response);
        
        // 验证序列化成功
        assertNotNull(json);
        assertFalse(json.isEmpty());
        assertTrue(json.contains("\"message\":\"Success\""));
        assertTrue(json.contains("\"total\":5"));
        
        System.out.println("复杂数据结构序列化成功");
    }

    @Test
    void testLocationSerializationUtilMethods() {
        // 测试LocationSerializationUtil的各种方法
        Point testPoint = geometryFactory.createPoint(new Coordinate(120.123456, 30.654321));
        
        // 测试pointToString
        String locationStr = LocationSerializationUtil.pointToString(testPoint);
        assertEquals("120.123456,30.654321", locationStr);
        
        // 测试stringToPoint
        Point convertedPoint = LocationSerializationUtil.stringToPoint(locationStr);
        assertNotNull(convertedPoint);
        assertEquals(120.123456, convertedPoint.getX(), 0.000001);
        assertEquals(30.654321, convertedPoint.getY(), 0.000001);
        
        // 测试pointToMap
        var locationMap = LocationSerializationUtil.pointToMap(testPoint);
        assertNotNull(locationMap);
        assertEquals(120.123456, locationMap.get("longitude"));
        assertEquals(30.654321, locationMap.get("latitude"));
        
        // 测试mapToPoint
        Point mapPoint = LocationSerializationUtil.mapToPoint(locationMap);
        assertNotNull(mapPoint);
        assertEquals(120.123456, mapPoint.getX(), 0.000001);
        assertEquals(30.654321, mapPoint.getY(), 0.000001);
        
        System.out.println("LocationSerializationUtil所有方法测试通过");
    }

    @Test
    void testNullAndEmptyLocationHandling() throws Exception {
        // 测试null位置的处理
        Event eventWithNullLocation = new Event();
        eventWithNullLocation.setId(999L);
        eventWithNullLocation.setLocation(null);
        
        String json1 = objectMapper.writeValueAsString(eventWithNullLocation);
        assertTrue(json1.contains("\"locationInfo\":null"));
        
        // 测试空位置的处理
        Event eventWithEmptyLocation = new Event();
        eventWithEmptyLocation.setId(998L);
        eventWithEmptyLocation.setLocation(geometryFactory.createPoint());
        
        String json2 = objectMapper.writeValueAsString(eventWithEmptyLocation);
        assertTrue(json2.contains("\"locationInfo\":null"));
        
        System.out.println("null和空位置处理测试通过");
    }

    private Event createTestEvent(int index) {
        Event event = new Event();
        event.setId((long) index);
        event.setWasteMaterial(1L);
        event.setWasteSize(2L);
        event.setDiscoveryMethod(0L);
        event.setConfidence(BigDecimal.valueOf(0.85 + index * 0.01));
        event.setDiscoveryTime(new Date());
        event.setEventStatus(1L);
        
        // 创建不同的位置坐标
        Point location = geometryFactory.createPoint(
            new Coordinate(120.0 + index * 0.01, 30.0 + index * 0.01)
        );
        event.setLocation(location);
        
        return event;
    }

    private EventDetailVO createTestEventDetailVO(int index) {
        EventDetailVO vo = new EventDetailVO();
        vo.setId((long) index);
        vo.setEventTitle("Test Event " + index);
        vo.setWasteSizeDesc("Small");
        vo.setWasteMaterialDesc("Plastic");
        vo.setGridName("Grid " + index);
        vo.setDiscoveryMethodDesc("AI Detection");
        vo.setConfidence(BigDecimal.valueOf(0.85 + index * 0.01));
        vo.setEventStatus(1L);
        vo.setDiscoveryTime(new Date());
        
        // 设置经纬度字符串
        vo.setLongitude(String.valueOf(120.0 + index * 0.01));
        vo.setLatitude(String.valueOf(30.0 + index * 0.01));
        
        return vo;
    }
}
