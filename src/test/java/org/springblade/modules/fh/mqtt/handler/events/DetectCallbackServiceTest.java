package org.springblade.modules.fh.mqtt.handler.events;

import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DetectCallbackService测试类
 * 验证位置字符串到JTS Point对象的转换功能
 */
class DetectCallbackServiceTest {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory(new PrecisionModel(), 4326);

    /**
     * 模拟DetectCallbackServiceImpl中的createPointFromLocationString方法
     */
    private static Point createPointFromLocationString(String locationStr) {
        try {
            if (locationStr == null || locationStr.trim().isEmpty()) {
                locationStr = "0.0,0.0";
            }
            
            String[] coordinates = locationStr.split(",");
            if (coordinates.length != 2) {
                System.out.println("位置字符串格式错误: " + locationStr + ", 使用默认坐标");
                return GEOMETRY_FACTORY.createPoint(new Coordinate(0.0, 0.0));
            }
            
            double longitude = Double.parseDouble(coordinates[0].trim());
            double latitude = Double.parseDouble(coordinates[1].trim());
            
            return GEOMETRY_FACTORY.createPoint(new Coordinate(longitude, latitude));
            
        } catch (Exception e) {
            System.err.println("创建Point对象失败，位置字符串: " + locationStr + ", 错误: " + e.getMessage());
            // 返回默认坐标点
            return GEOMETRY_FACTORY.createPoint(new Coordinate(0.0, 0.0));
        }
    }

    @Test
    void testCreatePointFromValidLocationString() {
        // 测试有效的位置字符串
        String locationStr = "120.123456,30.654321";
        Point point = createPointFromLocationString(locationStr);
        
        assertNotNull(point);
        assertEquals(120.123456, point.getX(), 0.000001);
        assertEquals(30.654321, point.getY(), 0.000001);
        assertEquals(4326, point.getSRID());
    }

    @Test
    void testCreatePointFromNullLocationString() {
        // 测试null位置字符串
        Point point = createPointFromLocationString(null);
        
        assertNotNull(point);
        assertEquals(0.0, point.getX(), 0.000001);
        assertEquals(0.0, point.getY(), 0.000001);
    }

    @Test
    void testCreatePointFromEmptyLocationString() {
        // 测试空位置字符串
        Point point = createPointFromLocationString("");
        
        assertNotNull(point);
        assertEquals(0.0, point.getX(), 0.000001);
        assertEquals(0.0, point.getY(), 0.000001);
    }

    @Test
    void testCreatePointFromInvalidLocationString() {
        // 测试无效格式的位置字符串
        Point point = createPointFromLocationString("invalid,format,extra");
        
        assertNotNull(point);
        assertEquals(0.0, point.getX(), 0.000001);
        assertEquals(0.0, point.getY(), 0.000001);
    }

    @Test
    void testCreatePointFromLocationStringWithSpaces() {
        // 测试带空格的位置字符串
        String locationStr = " 120.123456 , 30.654321 ";
        Point point = createPointFromLocationString(locationStr);
        
        assertNotNull(point);
        assertEquals(120.123456, point.getX(), 0.000001);
        assertEquals(30.654321, point.getY(), 0.000001);
    }

    @Test
    void testCreatePointFromLocationStringWithInvalidNumbers() {
        // 测试包含无效数字的位置字符串
        Point point = createPointFromLocationString("abc,def");
        
        assertNotNull(point);
        assertEquals(0.0, point.getX(), 0.000001);
        assertEquals(0.0, point.getY(), 0.000001);
    }
}
