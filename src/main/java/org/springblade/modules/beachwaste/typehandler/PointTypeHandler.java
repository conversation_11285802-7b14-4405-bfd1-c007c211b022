package org.springblade.modules.beachwaste.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.postgis.PGgeometry;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * MyBatis TypeHandler for JTS Point and PostGIS GEOMETRY conversion
 * Handles conversion between JTS Point objects and PostGIS geometry types
 */
@MappedJdbcTypes(JdbcType.OTHER)
@MappedTypes(Point.class)
public class PointTypeHandler extends BaseTypeHandler<Point> {

    private static final Logger logger = LoggerFactory.getLogger(PointTypeHandler.class);
    private static final WKTReader wktReader = new WKTReader();
    private static final WKTWriter wktWriter = new WKTWriter();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Point parameter, JdbcType jdbcType) throws SQLException {
        try {
            // Convert JTS Point to WKT string and create PGobject
            String wkt = wktWriter.write(parameter);
            PGobject pgObject = new PGobject();
            pgObject.setType("geometry");
            pgObject.setValue(wkt);
            ps.setObject(i, pgObject);

            if (logger.isDebugEnabled()) {
                logger.debug("Set Point parameter: {} as WKT: {}", parameter, wkt);
            }
        } catch (Exception e) {
            logger.error("Error setting Point parameter: {}", e.getMessage(), e);
            throw new SQLException("Cannot convert Point to PostGIS geometry", e);
        }
    }

    @Override
    public Point getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object obj = rs.getObject(columnName);
        if (obj == null) {
            return null;
        }
        return convertToPoint(obj, "column[" + columnName + "]");
    }

    @Override
    public Point getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object obj = rs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        return convertToPoint(obj, "column[" + columnIndex + "]");
    }

    @Override
    public Point getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object obj = cs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        return convertToPoint(obj, "callable[" + columnIndex + "]");
    }

    /**
     * Convert database object to JTS Point
     * @param obj Database returned object
     * @param context Context information for logging
     * @return JTS Point object
     * @throws SQLException Thrown when conversion fails
     */
    private Point convertToPoint(Object obj, String context) throws SQLException {
        if (obj == null) {
            return null;
        }

        try {
            String wktString = null;

            if (obj instanceof PGgeometry) {
                // Handle PGgeometry objects
                PGgeometry pgGeometry = (PGgeometry) obj;
                org.postgis.Geometry postgisGeom = pgGeometry.getGeometry();

                if (postgisGeom instanceof org.postgis.Point) {
                    org.postgis.Point postgisPoint = (org.postgis.Point) postgisGeom;
                    // Convert PostGIS Point to WKT
                    wktString = "POINT(" + postgisPoint.getX() + " " + postgisPoint.getY() + ")";
                } else {
                    logger.warn("PostGIS geometry from {} is not Point type: {}", context, postgisGeom.getType());
                    throw new SQLException("Expected PostGIS Point type, but got: " + postgisGeom.getType());
                }
            } else if (obj instanceof PGobject) {
                // Handle PGobject (common case for PostGIS)
                PGobject pgObject = (PGobject) obj;
                if ("geometry".equals(pgObject.getType()) || "point".equals(pgObject.getType())) {
                    wktString = pgObject.getValue();
                } else {
                    logger.warn("PGobject from {} is not geometry type: {}", context, pgObject.getType());
                    throw new SQLException("Expected geometry PGobject, but got: " + pgObject.getType());
                }
            } else if (obj instanceof String) {
                // Handle direct WKT string
                wktString = (String) obj;
            } else {
                logger.error("Unsupported database object type: {} from {}", obj.getClass().getName(), context);
                throw new SQLException("Unsupported database object type: " + obj.getClass().getName());
            }

            // Parse WKT string to JTS Point
            if (wktString != null && !wktString.trim().isEmpty()) {
                if (logger.isDebugEnabled()) {
                    logger.debug("Parsing WKT from {}: {}", context, wktString);
                }

                Geometry geometry = wktReader.read(wktString);
                if (geometry instanceof Point) {
                    Point point = (Point) geometry;
                    if (logger.isDebugEnabled()) {
                        logger.debug("Successfully converted Point from {}: ({}, {})",
                                   context, point.getX(), point.getY());
                    }
                    return point;
                } else {
                    logger.warn("WKT from {} is not Point type: {}", context, geometry.getGeometryType());
                    throw new SQLException("WKT is not Point type: " + geometry.getGeometryType());
                }
            } else {
                logger.warn("Empty or null WKT string from {}", context);
                return null;
            }

        } catch (ParseException e) {
            logger.error("Error parsing WKT string from {}: {}", context, e.getMessage(), e);
            throw new SQLException("Failed to parse WKT string: " + e.getMessage(), e);
        } catch (SQLException e) {
            throw e; // Re-throw SQLException as-is
        } catch (Exception e) {
            logger.error("Unexpected error converting Point from {}: {}", context, e.getMessage(), e);
            throw new SQLException("Error converting Point: " + e.getMessage(), e);
        }
    }
}
