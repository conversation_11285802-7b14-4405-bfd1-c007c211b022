package org.springblade.modules.beachwaste.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 事件信息Excel导出实体类
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class EventExcel implements Serializable, LocationSettable {

    private static final long serialVersionUID = 1L;

	/**
	 * 事件标题
	 */
	@ColumnWidth(20)
	@ExcelProperty("事件标题")
	private String eventTitle;

    /**
     * 事件编号
     */
    @ColumnWidth(15)
    @ExcelProperty("事件编号")
    private String id;

    /**
     * 网格名称
     */
    @ColumnWidth(20)
    @ExcelProperty("网格名称")
    private String gridName;

    /**
     * 发现时间
     */
    @ColumnWidth(20)
    @ExcelProperty("发现时间")
    private Date discoveryTime;

    /**
     * 发现经度
     */
    @ColumnWidth(15)
    @ExcelProperty("发现经度")
    private String longitude;

    /**
     * 发现纬度
     */
    @ColumnWidth(15)
    @ExcelProperty("发现纬度")
    private String latitude;

    /**
     * 置信度
     */
    @ColumnWidth(15)
    @ExcelProperty("置信度")
    private String confidence;

    /**
     * 垃圾尺寸分类
     */
    @ColumnWidth(20)
    @ExcelProperty("垃圾尺寸分类")
    private String wasteSizeDesc;

    /**
     * 垃圾材质分类
     */
    @ColumnWidth(20)
    @ExcelProperty("垃圾材质分类")
    private String wasteMaterialDesc;

    /**
     * 来源分类
     */
    @ColumnWidth(20)
    @ExcelProperty("来源分类")
    private String discoveryMethodDesc;

    /**
     * 网格员
     */
    @ColumnWidth(15)
    @ExcelProperty("网格员")
    private String handlerStaffName;

}
