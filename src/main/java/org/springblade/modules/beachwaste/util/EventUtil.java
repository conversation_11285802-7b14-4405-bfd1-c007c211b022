package org.springblade.modules.beachwaste.util;

import org.locationtech.jts.geom.Point;
import org.springblade.modules.beachwaste.excel.EventExcel;
import org.springblade.modules.beachwaste.excel.EventExportExcel;
import org.springblade.modules.beachwaste.excel.LocationSettable;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.enums.WasteMaterialEnum;
import org.springblade.modules.beachwaste.enums.WasteSizeEnum;
import org.springblade.modules.beachwaste.enums.DiscoveryMethodEnum;
import org.springblade.modules.beachwaste.enums.EventStatusEnum;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 事件工具类，提供事件相关的数据转换和格式化方法
 *
 * <AUTHOR>
 */
public class EventUtil {

    /**
     * 格式化置信度
     *
     * @param confidence 置信度
     * @return 格式化后的置信度字符串
     */
    public static String formatConfidence(BigDecimal confidence) {
        if (confidence == null) {
            return formatConfidence(0.0);
        }
        return formatConfidence(confidence.doubleValue());
    }

    /**
     * 格式化置信度
     *
     * @param confidence 置信度
     * @return 格式化后的置信度字符串
     */
    public static String formatConfidence(double confidence) {
        return String.format("%.2f%%", confidence * 1);
    }

    /**
     * 构建事件标题
     *
     * @param event 事件对象
     * @return 事件标题
     */
    public static String buildEventTitle(Event event) {
        if (event == null) {
            return "未知事件";
        }

        StringBuilder title = new StringBuilder();

        // 添加垃圾材质
        String wasteMaterial = getWasteMaterialDescription(event.getWasteMaterial());
        if (StringUtils.hasText(wasteMaterial)) {
            title.append(wasteMaterial);
        }

        // 添加垃圾大小
        String wasteSize = getWasteSizeDescription(event.getWasteSize());
        if (StringUtils.hasText(wasteSize)) {
            if (title.length() > 0) {
                title.append("-");
            }
            title.append(wasteSize);
        }

        // 如果没有任何描述，使用默认标题
        if (title.length() == 0) {
            title.append("海滩垃圾事件");
        }

        return title.toString();
    }

    /**
     * 获取垃圾材质描述
     */
    public static String getWasteMaterialDescription(Long wasteMaterial) {
        return WasteMaterialEnum.getDesc(wasteMaterial);
    }

    /**
     * 获取垃圾尺寸描述
     */
    public static String getWasteSizeDescription(Long wasteSize) {
        return WasteSizeEnum.getDesc(wasteSize);
    }

    /**
     * 获取发现方式描述
     */
    public static String getDiscoveryMethodDescription(Long discoveryMethod) {
        return DiscoveryMethodEnum.getDesc(discoveryMethod);
    }

    /**
     * 获取事件状态描述
     */
    public static String getEventStatusDescription(Long eventStatus) {
        return EventStatusEnum.getDesc(eventStatus);
    }

    /**
     * 转换为EventExcel对象列表
     *
     * @param eventList    事件列表
     * @param gridNameMap  网格名称映射
     * @param userNameMap  用户名称映射
     * @return EventExcel对象列表
     */
    public static List<EventExcel> convertToEventExcelList(List<Event> eventList, Map<Long, String> gridNameMap, Map<Long, String> userNameMap) {
        return eventList.stream()
                .map(event -> {
                    EventExcel eventExcel = new EventExcel();

                    // 基本信息
                    eventExcel.setId(String.valueOf(event.getId()));
                    eventExcel.setEventTitle(buildEventTitle(event));
                    eventExcel.setDiscoveryTime(event.getDiscoveryTime());
                    eventExcel.setDiscoveryMethodDesc(getDiscoveryMethodDescription(event.getDiscoveryMethod()));

                    // 垃圾信息
                    eventExcel.setWasteMaterialDesc(getWasteMaterialDescription(event.getWasteMaterial()));
                    eventExcel.setWasteSizeDesc(getWasteSizeDescription(event.getWasteSize()));
                    // 处理不同类型的confidence字段
                    eventExcel.setConfidence(formatConfidence(event.getConfidence()));

                    // 位置信息
                    setLocationData(eventExcel, event);

                    // 关联信息
                    eventExcel.setGridName(EventBatchUtil.getGridName(event.getGridId(), gridNameMap));
                    eventExcel.setHandlerStaffName(EventBatchUtil.getHandlerStaffName(event.getHandlerStaffId(), userNameMap));

                    return eventExcel;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 转换为EventExportExcel对象列表
     *
     * @param eventList    事件列表
     * @param gridNameMap  网格名称映射
     * @param userNameMap  用户名称映射
     * @return EventExportExcel对象列表
     */
    public static List<EventExportExcel> convertToEventExportExcelList(List<Event> eventList,
																	   Map<Long, String> gridNameMap,
																	   Map<Long, String> userNameMap) {
        return eventList.stream()
                .map(event -> {
                    EventExportExcel excel = convertToEventExportExcel(event, gridNameMap, userNameMap);
                    if (excel != null) {
                        // 处理不同类型的confidence字段
                        excel.setConfidence(formatConfidence(event.getConfidence()));
                    }
                    return excel;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 转换为EventExportExcel对象
     *
     * @param event       事件对象
     * @param gridNameMap 网格名称映射
     * @param userNameMap 用户名称映射
     * @return EventExportExcel对象
     */
    public static EventExportExcel convertToEventExportExcel(Event event,
															 Map<Long, String> gridNameMap,
															 Map<Long, String> userNameMap) {
        if (event == null) {
            return null;
        }

        EventExportExcel excel = new EventExportExcel();
        excel.setEventId(event.getId());

        // 安全地设置描述字段，添加异常处理
        // 设置垃圾材质描述
        String wasteMaterialDesc = getWasteMaterialDescription(event.getWasteMaterial());
        excel.setWasteMaterialDesc(wasteMaterialDesc != null ? wasteMaterialDesc : "未知材质");

        // 设置垃圾尺寸描述
        String wasteSizeDesc = getWasteSizeDescription(event.getWasteSize());
        excel.setWasteSizeDesc(wasteSizeDesc != null ? wasteSizeDesc : "未知尺寸");

        // 设置发现方式描述
        String discoveryMethodDesc = getDiscoveryMethodDescription(event.getDiscoveryMethod());
        excel.setDiscoveryMethodDesc(discoveryMethodDesc != null ? discoveryMethodDesc : "未知方式");

        // 设置事件状态描述
        String eventStatusDesc = getEventStatusDescription(event.getEventStatus());
        excel.setEventStatusDesc(eventStatusDesc != null ? eventStatusDesc : "未知状态");

        try {
            excel.setConfidence(formatConfidence(event.getConfidence()));
        } catch (Exception e) {
            excel.setConfidence("0");
        }

        excel.setDiscoveryTime(event.getDiscoveryTime());

        // 安全地设置网格名称
        try {
            excel.setGridName(gridNameMap != null ? gridNameMap.getOrDefault(event.getGridId(), "未知网格") : "未知网格");
        } catch (Exception e) {
            excel.setGridName("获取异常");
        }

        // 安全地设置处理人员名称
        try {
            excel.setHandlerStaffName(userNameMap != null ? userNameMap.getOrDefault(event.getHandlerStaffId(), "未分配") : "未分配");
        } catch (Exception e) {
            excel.setHandlerStaffName("获取异常");
        }

        // 设置位置信息
        try {
            setLocationData(excel, event);
        } catch (Exception e) {
            // 位置信息设置失败时不影响其他字段
        }

        return excel;
    }

    /**
     * 设置位置数据
     * 通用方法，支持所有实现LocationSettable接口的对象
     * 从JTS Point对象中提取经纬度坐标
     *
     * @param locationSettable 实现LocationSettable接口的导出对象
     * @param event           事件对象
     */
    public static void setLocationData(LocationSettable locationSettable, Event event) {
        if (event == null || locationSettable == null) {
            return;
        }

        Point location = event.getLocation();
        if (location != null && !location.isEmpty()) {
            try {
                // 从JTS Point对象中提取坐标
                double longitude = location.getX(); // 经度
                double latitude = location.getY();  // 纬度

                // 格式化坐标，保留6位小数
                DecimalFormat df = new DecimalFormat("#.######");
                locationSettable.setLongitude(df.format(longitude));
                locationSettable.setLatitude(df.format(latitude));

            } catch (Exception e) {
                // 如果坐标提取失败，设置默认值
                locationSettable.setLongitude("0.000000");
                locationSettable.setLatitude("0.000000");
            }
        } else {
            // 如果location为null或空，设置默认值
            locationSettable.setLongitude("0.000000");
            locationSettable.setLatitude("0.000000");
        }
    }

}
