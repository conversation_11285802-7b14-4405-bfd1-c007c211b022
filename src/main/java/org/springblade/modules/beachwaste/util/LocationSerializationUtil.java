package org.springblade.modules.beachwaste.util;

import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;

import java.util.HashMap;
import java.util.Map;

/**
 * 位置信息序列化工具类
 * 提供JTS Point对象与可序列化格式之间的转换方法
 * 
 * <AUTHOR>
 */
@Slf4j
public class LocationSerializationUtil {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory(new PrecisionModel(), 4326);

    /**
     * 将JTS Point对象转换为可序列化的Map
     * 
     * @param point JTS Point对象
     * @return 包含经纬度信息的Map，如果point为null或空则返回null
     */
    public static Map<String, Object> pointToMap(Point point) {
        if (point == null || point.isEmpty()) {
            return null;
        }
        
        try {
            Map<String, Object> locationMap = new HashMap<>();
            locationMap.put("longitude", point.getX());
            locationMap.put("latitude", point.getY());
            
            if (point.getSRID() != 0) {
                locationMap.put("srid", point.getSRID());
            }
            
            return locationMap;
        } catch (Exception e) {
            log.error("转换Point对象为Map失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将JTS Point对象转换为经纬度字符串
     * 
     * @param point JTS Point对象
     * @return 格式为 "经度,纬度" 的字符串，如果point为null或空则返回 "0.0,0.0"
     */
    public static String pointToString(Point point) {
        if (point == null || point.isEmpty()) {
            return "0.0,0.0";
        }
        
        try {
            return point.getX() + "," + point.getY();
        } catch (Exception e) {
            log.error("转换Point对象为字符串失败: {}", e.getMessage());
            return "0.0,0.0";
        }
    }

    /**
     * 从经纬度字符串创建JTS Point对象
     * 
     * @param locationStr 格式为 "经度,纬度" 的字符串
     * @return JTS Point对象，如果解析失败则返回默认坐标(0,0)
     */
    public static Point stringToPoint(String locationStr) {
        if (locationStr == null || locationStr.trim().isEmpty()) {
            return GEOMETRY_FACTORY.createPoint(new Coordinate(0.0, 0.0));
        }
        
        try {
            String[] coordinates = locationStr.split(",");
            if (coordinates.length != 2) {
                log.warn("位置字符串格式错误: {}, 使用默认坐标", locationStr);
                return GEOMETRY_FACTORY.createPoint(new Coordinate(0.0, 0.0));
            }
            
            double longitude = Double.parseDouble(coordinates[0].trim());
            double latitude = Double.parseDouble(coordinates[1].trim());
            
            return GEOMETRY_FACTORY.createPoint(new Coordinate(longitude, latitude));
            
        } catch (Exception e) {
            log.error("从字符串创建Point对象失败，位置字符串: {}, 错误: {}", locationStr, e.getMessage());
            return GEOMETRY_FACTORY.createPoint(new Coordinate(0.0, 0.0));
        }
    }

    /**
     * 从Map创建JTS Point对象
     * 
     * @param locationMap 包含longitude和latitude键的Map
     * @return JTS Point对象，如果解析失败则返回默认坐标(0,0)
     */
    public static Point mapToPoint(Map<String, Object> locationMap) {
        if (locationMap == null || locationMap.isEmpty()) {
            return GEOMETRY_FACTORY.createPoint(new Coordinate(0.0, 0.0));
        }
        
        try {
            Object lonObj = locationMap.get("longitude");
            Object latObj = locationMap.get("latitude");
            
            if (lonObj == null || latObj == null) {
                log.warn("Map中缺少经纬度信息，使用默认坐标");
                return GEOMETRY_FACTORY.createPoint(new Coordinate(0.0, 0.0));
            }
            
            double longitude = Double.parseDouble(lonObj.toString());
            double latitude = Double.parseDouble(latObj.toString());
            
            Point point = GEOMETRY_FACTORY.createPoint(new Coordinate(longitude, latitude));
            
            // 如果有SRID信息，设置它
            Object sridObj = locationMap.get("srid");
            if (sridObj != null) {
                try {
                    int srid = Integer.parseInt(sridObj.toString());
                    point.setSRID(srid);
                } catch (NumberFormatException e) {
                    log.warn("无效的SRID值: {}", sridObj);
                }
            }
            
            return point;
            
        } catch (Exception e) {
            log.error("从Map创建Point对象失败: {}", e.getMessage());
            return GEOMETRY_FACTORY.createPoint(new Coordinate(0.0, 0.0));
        }
    }

    /**
     * 提取Point对象的经度
     * 
     * @param point JTS Point对象
     * @return 经度值，如果point为null或空则返回0.0
     */
    public static double getLongitude(Point point) {
        if (point == null || point.isEmpty()) {
            return 0.0;
        }
        
        try {
            return point.getX();
        } catch (Exception e) {
            log.error("获取Point经度失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 提取Point对象的纬度
     * 
     * @param point JTS Point对象
     * @return 纬度值，如果point为null或空则返回0.0
     */
    public static double getLatitude(Point point) {
        if (point == null || point.isEmpty()) {
            return 0.0;
        }
        
        try {
            return point.getY();
        } catch (Exception e) {
            log.error("获取Point纬度失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 验证Point对象是否有效
     * 
     * @param point JTS Point对象
     * @return 如果Point对象不为null且不为空则返回true
     */
    public static boolean isValidPoint(Point point) {
        return point != null && !point.isEmpty();
    }
}
