package org.springblade.modules.beachwaste.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * 事件查询工具类，提供事件查询条件构建相关的公共方法
 *
 * <AUTHOR>
 */
@Slf4j
public class EventQueryUtil {

    /**
     * 日期格式：yyyy-MM-dd
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 构建事件查询条件
     *
     * @param discoveryMethod 发现方式
     * @param eventStatus     事件状态
     * @param gridId          网格ID
     * @param startDate       开始日期
     * @param endDate         结束日期
     * @return 查询条件
     */
    public static LambdaQueryWrapper<Event> buildEventQueryWrapper(Long discoveryMethod, Long eventStatus, Long gridId, String startDate, String endDate) {
        LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<Event>()
            .eq(Objects.nonNull(discoveryMethod), Event::getDiscoveryMethod, discoveryMethod)
            .eq(Objects.nonNull(eventStatus), Event::getEventStatus, eventStatus)
            .eq(Objects.nonNull(gridId), Event::getGridId, gridId);

        // 处理日期范围
        addDateRangeConditions(queryWrapper, startDate, endDate);

        return queryWrapper.orderByDesc(Event::getDiscoveryTime).orderByDesc(Event::getId);
    }

    /**
     * 构建日期范围查询条件
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 查询条件
     */
    public static LambdaQueryWrapper<Event> buildDateRangeQueryWrapper(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate, DATE_FORMATTER);
        LocalDate end = LocalDate.parse(endDate, DATE_FORMATTER);

        LocalDateTime startDateTime = start.atTime(LocalTime.MIN);
        LocalDateTime endDateTime = end.atTime(LocalTime.MAX);

        Date startDateConverted = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
        Date endDateConverted = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());

        return new LambdaQueryWrapper<Event>()
            .between(Event::getDiscoveryTime, startDateConverted, endDateConverted)
            .orderByDesc(Event::getDiscoveryTime);
    }

    /**
     * 添加日期范围条件到查询构造器
     *
     * @param queryWrapper 查询构造器
     * @param startDate    开始日期
     * @param endDate      结束日期
     */
    public static void addDateRangeConditions(LambdaQueryWrapper<Event> queryWrapper, String startDate, String endDate) {
        // 处理开始日期
        if (StringUtils.hasText(startDate)) {
            try {
                LocalDate start = LocalDate.parse(startDate, DATE_FORMATTER);
                LocalDateTime startDateTime = start.atTime(LocalTime.MIN);
                queryWrapper.ge(Event::getDiscoveryTime, Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            } catch (Exception e) {
                log.error("开始日期格式错误: {}", startDate, e);
            }
        }

        // 处理结束日期
        if (StringUtils.hasText(endDate)) {
            try {
                LocalDate end = LocalDate.parse(endDate, DATE_FORMATTER);
                LocalDateTime endDateTime = end.atTime(LocalTime.MAX);
                queryWrapper.le(Event::getDiscoveryTime, Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            } catch (Exception e) {
                log.error("结束日期格式错误: {}", endDate, e);
            }
        }
    }

    /**
     * 构建基础事件查询条件（不包含日期范围）
     *
     * @param discoveryMethod 发现方式
     * @param eventStatus     事件状态
     * @param gridId          网格ID
     * @return 查询条件
     */
    public static LambdaQueryWrapper<Event> buildBasicEventQueryWrapper(Long discoveryMethod, Long eventStatus, Long gridId) {
        return new LambdaQueryWrapper<Event>()
            .eq(Objects.nonNull(discoveryMethod), Event::getDiscoveryMethod, discoveryMethod)
            .eq(Objects.nonNull(eventStatus), Event::getEventStatus, eventStatus)
            .eq(Objects.nonNull(gridId), Event::getGridId, gridId)
            .orderByDesc(Event::getDiscoveryTime);
    }

}
