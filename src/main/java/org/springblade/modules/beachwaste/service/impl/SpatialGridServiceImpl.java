package org.springblade.modules.beachwaste.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.enums.EventStatusEnum;
import org.springblade.modules.beachwaste.mapper.EventMapper;
import org.springblade.modules.beachwaste.mapper.SpatialGridMapper;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.pojo.vo.SpatialGridDetailVo;
import org.springblade.modules.beachwaste.pojo.vo.SpatialGridInfoVo;
import org.springblade.modules.beachwaste.pojo.vo.SpatialGridListVo;
import org.springblade.modules.beachwaste.service.IGridScheduleService;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springblade.modules.beachwaste.util.*;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 空间网格服务实现类
 * <p>
 * 该类负责实现空间网格相关的业务逻辑，包括网格的增删改查、KML文件解析、批量导入等功能。
 * 继承自MyBatis-Plus的ServiceImpl，实现了ISpatialGridService接口。
 * </p>
 *
 * <AUTHOR>
@Slf4j
@Service
@AllArgsConstructor
public class SpatialGridServiceImpl extends ServiceImpl<SpatialGridMapper, SpatialGrid> implements ISpatialGridService {

	/**
	 * 用户服务接口，用于获取网格管理员信息
	 */
	private IUserService userService;

	/**
	 * 事件Mapper接口，用于查询网格相关的事件数据
	 */
	private EventMapper eventMapper;

	/**
	 * 网格排班服务接口，用于获取网格排班信息
	 */
	private IGridScheduleService gridScheduleService;

	/**
	 * 保存网格数据前进行校验
	 * <p>
	 * 该方法在保存网格数据前进行一系列校验，包括：
	 * 1. 基础数据校验（网格名称、编码等）
	 * 2. 管理员存在性校验
	 * 3. 数据唯一性校验（网格编码、名称不重复）
	 * 4. 兼容性校验（保持与旧版本的兼容）
	 * </p>
	 *
	 * @param spatialGrid 待保存的网格数据对象
	 * @return 校验结果，成功返回保存状态，失败返回错误信息
	 */
	@Override
	public R checkToSave(SpatialGrid spatialGrid) {
		// 校验基础数据
		String basicValidationResult = SpatialGridValidationUtil.validateGridBasicData(spatialGrid);
		if (basicValidationResult != null) {
			return R.fail(basicValidationResult);
		}

		// 校验管理员是否存在
		String inspectorValidationResult = SpatialGridValidationUtil.validateInspectorExists(spatialGrid.getUserId());
		if (inspectorValidationResult != null) {
			return R.fail(inspectorValidationResult);
		}

		// 校验数据唯一性
		String uniquenessValidationResult = SpatialGridValidationUtil.validateGridUniqueness(
			spatialGrid.getGridCode(), spatialGrid.getGridName());
		if (uniquenessValidationResult != null) {
			return R.fail(uniquenessValidationResult);
		}

		// 调用原有校验逻辑（保持兼容性）
		if (ValidationUtil.checkBase(spatialGrid.getUserId(), spatialGrid.getGridCode(), spatialGrid.getGridName())) {
			return R.fail("网格数据只能关联管理员角色");
		}

		return R.status(this.save(spatialGrid));
	}

	/**
	 * 更新网格数据前进行校验
	 * <p>
	 * 该方法在更新网格数据前进行一系列校验，包括：
	 * 1. 基础数据校验（网格名称、编码等）
	 * 2. 管理员存在性校验
	 * 3. 数据唯一性校验（更新时排除自身ID）
	 * 4. 兼容性校验（保持与旧版本的兼容）
	 * </p>
	 *
	 * @param spatialGrid 待更新的网格数据对象
	 * @return 校验结果，成功返回更新状态，失败返回错误信息
	 */
	@Override
	public R checkToUpdateById(SpatialGrid spatialGrid) {
		// 校验基础数据
		String basicValidationResult = SpatialGridValidationUtil.validateGridBasicData(spatialGrid);
		if (basicValidationResult != null) {
			return R.fail(basicValidationResult);
		}

		// 校验管理员是否存在
		String inspectorValidationResult = SpatialGridValidationUtil.validateInspectorExists(spatialGrid.getUserId());
		if (inspectorValidationResult != null) {
			return R.fail(inspectorValidationResult);
		}

		// 校验数据唯一性（更新时）
		String uniquenessValidationResult = SpatialGridValidationUtil.validateGridUniquenessForUpdate(
			spatialGrid.getId(), spatialGrid.getGridCode(), spatialGrid.getGridName());
		if (uniquenessValidationResult != null) {
			return R.fail(uniquenessValidationResult);
		}

		// 调用原有校验逻辑（保持兼容性）
		if (ValidationUtil.checkBaseForUpdate(spatialGrid.getId(), spatialGrid.getUserId(), spatialGrid.getGridCode(), spatialGrid.getGridName())) {
			return R.fail("网格数据只能关联管理员角色");
		}

		return R.status(this.updateById(spatialGrid));
	}

	/**
	 * 获取网格详情及相关事件信息
	 * <p>
	 * 该方法根据网格ID和年月查询网格详情，包括：
	 * 1. 网格基本信息
	 * 2. 网格管理员联系方式
	 * 3. 指定月份内已处理的事件数量
	 * </p>
	 *
	 * @param id 网格ID
	 * @param yearMonth 年月字符串，格式为"yyyy-MM"
	 * @return 包含网格详情和事件统计的结果
	 */
	@Override
	public R getGridDetailWithEvents(Long id, String yearMonth) {
		// 校验网格是否存在
		String gridValidationResult = SpatialGridValidationUtil.validateGridExists(id);
		if (gridValidationResult != null) {
			return R.fail(gridValidationResult);
		}

		// 校验年月格式
		String yearMonthValidationResult = SpatialGridValidationUtil.validateYearMonthFormat(yearMonth);
		if (yearMonthValidationResult != null) {
			return R.fail(yearMonthValidationResult);
		}

		SpatialGrid grid = this.getById(id);

		// 查询本月已处理的事件
		LambdaQueryWrapper<Event> wq = new LambdaQueryWrapper<>();
		wq.eq(Event::getGridId, id);
		wq.eq(Event::getEventStatus, EventStatusEnum.PROCESSED.getId());

		// 使用Java日期API计算月份的开始和结束 LocalDateTime
		YearMonth ym = YearMonth.parse(yearMonth);
		LocalDateTime startDateTime = ym.atDay(1).atStartOfDay();
		LocalDateTime endDateTime = ym.atEndOfMonth().atTime(LocalTime.MAX);

		// 转换为Date对象进行比较
		Date startDate = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
		Date endDate = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());
		wq.between(Event::getHandlerReportTime, startDate, endDate);

		// 使用 eventMapper 替代 eventService
		long count = eventMapper.selectCount(wq);

		// 使用VO封装返回结果
		SpatialGridDetailVo detailVo = new SpatialGridDetailVo();
		detailVo.setGrid(grid);
		// 获取管理员联系方式
		User inspector = userService.getById(grid.getUserId());
		detailVo.setPhoneNumber(inspector != null ? inspector.getPhone() : null);
		detailVo.setProcessedCount(count);

		return R.data(detailVo);
	}

	/**
	 * 解析KML文件内容并保存坐标数据
	 * <p>
	 * 该方法负责解析上传的KML文件，提取其中的地理信息，并将其保存为网格数据。
	 * 处理流程包括：
	 * 1. 校验KML文件格式和内容
	 * 2. 校验管理员是否存在
	 * 3. 校验网格编码和名称的唯一性
	 * 4. 解析KML文件中的地理要素和坐标
	 * 5. 处理几何数据并计算面积
	 * 6. 保存网格数据
	 * </p>
	 *
	 * @param file KML格式的上传文件
	 * @param id 管理员用户ID
	 * @param gridName 网格名称
	 * @param gridCode 网格编码
	 * @return 解析结果，成功返回成功信息，失败返回错误信息
	 */
	@Override
	public R parseKmlFile(MultipartFile file, Long id, String gridName, String gridCode) {
		// 校验KML文件
		String fileValidationResult = SpatialGridValidationUtil.validateKmlFile(file);
		if (fileValidationResult != null) {
			return R.fail(fileValidationResult);
		}

		// 校验管理员是否存在
		String inspectorValidationResult = SpatialGridValidationUtil.validateInspectorExists(id);
		if (inspectorValidationResult != null) {
			return R.fail(inspectorValidationResult);
		}

		// 校验数据唯一性
		String uniquenessValidationResult = SpatialGridValidationUtil.validateGridUniqueness(gridCode, gridName);
		if (uniquenessValidationResult != null) {
			return R.fail(uniquenessValidationResult);
		}

		// 调用原有校验逻辑（保持兼容性）
		if (ValidationUtil.checkBase(id, gridCode, gridName)) {
			return R.fail("网格数据只能关联管理员角色");
		}

		SpatialGrid spatialGrid = new SpatialGrid();
		spatialGrid.setGridCode(gridCode);
		spatialGrid.setGridName(gridName);

		try {
			// 创建DOM解析器
			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
			DocumentBuilder builder = factory.newDocumentBuilder();
			Document doc = builder.parse(file.getInputStream());
			doc.getDocumentElement().normalize();

			// 校验Placemark标签数量
			int placemarkCount = KmlParserUtil.countPlacemarks(doc);
			if (placemarkCount > 1) {
				return R.fail("上传KML文件只能包含单个地理要素，当前检测到" + placemarkCount + "个地理要素");
			}

			// 获取KML文件中的GEOM类型
			String geomType = KmlParserUtil.getGeomType(doc);
			spatialGrid.setGeomType(geomType);

			// 解析坐标数据
			String[] coordinates = KmlParserUtil.parseCoordinates(doc);

			// 处理几何数据
			SpatialGridKmlUtil.processGeometryData(spatialGrid, geomType, coordinates);

			// 存储userId
			spatialGrid.setUserId(id);

			boolean save = this.save(spatialGrid);
			if (save) {
				return R.success("网格数据保存成功");
			} else {
				return R.fail("网格数据保存失败");
			}

		} catch (Exception e) {
			e.printStackTrace();
			return R.fail("网格导入失败：" + e.getMessage());
		}
	}

	/**
	 * 解析KML文件中的多个地理要素并返回解析结果
	 * <p>
	 * 该方法负责解析上传的KML文件中的多个地理要素，但不进行保存操作。
	 * 处理流程包括：
	 * 1. 解析KML文件
	 * 2. 提取所有地理要素信息
	 * 3. 为每个地理要素计算WKT、GeoJSON格式的几何数据和面积
	 * 4. 返回解析结果列表
	 * </p>
	 * <p>
	 * 与parseKmlFile方法不同，该方法支持解析包含多个地理要素的KML文件，
	 * 并且只返回解析结果而不进行保存，通常用于批量导入前的预览。
	 * </p>
	 *
	 * @param file 上传的KML文件对象
	 * @return 解析结果（包含多个地理要素信息的列表）
	 */
	@Override
	public R parseMultipleFeatures(MultipartFile file) {
		try {
			// 创建DOM解析器
			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
			DocumentBuilder builder = factory.newDocumentBuilder();
			Document doc = builder.parse(file.getInputStream());
			doc.getDocumentElement().normalize();

			// 获取KML文件中的所有地理要素
			List<SpatialGridInfoVo> features = KmlParserUtil.parseMultipleFeatures(doc);

			// 如果没有解析到任何地理要素，返回错误信息
			if (features.isEmpty()) {
				return R.fail("未在KML文件中检测到有效的地理要素");
			}

			// 为每个地理要素计算WKT、GeoJSON和面积
			for (SpatialGridInfoVo feature : features) {
				String geomType = feature.getGeomType();
				String[] coordinates = feature.getCoordinates();

				// 处理几何数据
				SpatialGridKmlUtil.processGeometryData(feature, geomType, coordinates);
			}

			return R.data(features);

		} catch (Exception e) {
			e.printStackTrace();
			return R.fail("KML解析失败：" + e.getMessage());
		}
	}

	/**
	 * 批量保存网格数据
	 * <p>
	 * 该方法负责批量保存网格数据列表，通常用于从KML文件批量导入网格数据。
	 * 处理流程包括：
	 * 1. 批量校验网格数据的有效性
	 * 2. 校验每个网格的编码和名称唯一性
	 * 3. 处理每个网格的几何数据并计算面积
	 * 4. 批量保存所有网格数据
	 * </p>
	 * <p>
	 * 该方法使用了@Transactional注解，确保在发生异常时能够回滚所有操作，
	 * 保证数据的一致性。
	 * </p>
	 *
	 * @param gridList 网格数据列表
	 * @return 保存结果，成功返回成功信息和保存数量，失败返回错误信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R batchSaveGrids(List<SpatialGrid> gridList) {
		try {
			// 批量校验网格数据
			String batchValidationResult = SpatialGridValidationUtil.validateGridListForBatch(gridList);
			if (batchValidationResult != null) {
				return R.fail(batchValidationResult);
			}

			// 处理每个网格的几何数据
			for (SpatialGrid grid : gridList) {
				// 校验网格编码和网格名称是否重复（保持兼容性）
				if (ValidationUtil.checkBase(grid.getUserId(), grid.getGridCode(), grid.getGridName())) {
					return R.fail("网格数据只能关联管理员角色，或网格编码/名称已存在");
				}

				// 计算多边形面积
				if (grid.getGeomType() != null && grid.getGridGeom() != null) {
					// 从WKT格式中提取坐标
					String wkt = grid.getGridGeom();
					String[] coordinates = SpatialGridKmlUtil.extractCoordinatesFromWkt(wkt);

					// 计算面积
					double area = GeometryUtil.calculateArea(grid.getGeomType(), coordinates);
					grid.setGridArea(BigDecimal.valueOf(area));
				}
			}

			// 批量保存
			boolean success = this.saveBatch(gridList);
			if (success) {
				return R.success("成功导入 " + gridList.size() + " 条网格数据");
			} else {
				return R.fail("批量导入失败");
			}
		} catch (Exception e) {
			e.printStackTrace();
			return R.fail("批量导入异常: " + e.getMessage());
		}
	}

	/**
	 * 获取带有排班信息的网格列表
	 * <p>
	 * 该方法根据查询条件获取网格列表，并附加每个网格的排班信息。
	 * 处理流程包括：
	 * 1. 根据查询条件筛选网格数据
	 * 2. 获取每个网格的排班信息
	 * 3. 获取每个网格管理员的信息
	 * 4. 组装返回结果
	 * </p>
	 *
	 * @param dto 查询条件数据传输对象，包含筛选条件和分页信息
	 * @return 包含网格列表和排班信息的结果
	 */
	@Override
	public R getGridListWithSchedule(GridScheduleQueryDTO dto) {
		try {
			// 使用查询工具类获取网格列表
			List<SpatialGridListVo> gridListVos = SpatialGridQueryUtil.
				getGridListWithScheduleData(dto, this, gridScheduleService, userService);

			return R.data(gridListVos);

		} catch (Exception e) {
			log.error("获取网格列表失败", e);
			return R.fail("获取网格列表失败：" + e.getMessage());
		}
	}

	/**
	 * 导出所有网格数据为KML格式
	 * <p>
	 * 该方法将系统中所有未删除的网格数据导出为KML格式的字符串。
	 * 处理流程包括：
	 * 1. 查询所有未删除的网格数据
	 * 2. 使用KML工具类将网格数据转换为KML格式
	 * 3. 返回生成的KML内容字符串
	 * </p>
	 * <p>
	 * 注意：该方法在发生异常时会抛出RuntimeException，调用方需要进行异常处理。
	 * </p>
	 *
	 * @param filename 导出文件名（目前仅用于标识，实际未使用）
	 * @return KML格式的字符串内容
	 * @throws RuntimeException 当没有找到网格数据或导出过程中发生错误时抛出
	 */
	@Override
	public String exportAllGridsToKml(String filename) {
		try {
			// 使用查询工具类构建查询条件
			LambdaQueryWrapper<SpatialGrid> queryWrapper = new LambdaQueryWrapper<SpatialGrid>();
			queryWrapper.eq(SpatialGrid::getIsDeleted, 0)
					.select(SpatialGrid::getId, SpatialGrid::getGridCode, SpatialGrid::getGridName,
					SpatialGrid::getGeomJson, SpatialGrid::getGeomType, SpatialGrid::getGridArea,
					SpatialGrid::getCreateTime);
			List<SpatialGrid> gridList = this.list(queryWrapper);

			if (gridList.isEmpty()) {
				throw new RuntimeException("没有找到网格数据");
			}

			// 使用KML工具类生成KML内容
			String kmlContent = SpatialGridKmlUtil.generateKmlContent(gridList);

			return kmlContent;
		} catch (Exception e) {
			log.error("导出KML失败", e);
			throw new RuntimeException("导出KML失败: " + e.getMessage());
		}
	}

}
