package org.springblade.modules.beachwaste.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.locationtech.jts.geom.Point;

import java.io.IOException;

/**
 * JTS Point对象的自定义Jackson序列化器
 * 将Point对象序列化为包含经纬度的JSON对象，避免循环引用问题
 * 
 * <AUTHOR>
 */
public class PointSerializer extends JsonSerializer<Point> {

    @Override
    public void serialize(Point point, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (point == null || point.isEmpty()) {
            gen.writeNull();
            return;
        }

        try {
            gen.writeStartObject();
            gen.writeNumberField("longitude", point.getX());
            gen.writeNumberField("latitude", point.getY());
            
            // 如果Point对象有SRID信息，也包含进去
            if (point.getSRID() != 0) {
                gen.writeNumberField("srid", point.getSRID());
            }
            
            gen.writeEndObject();
        } catch (Exception e) {
            // 如果序列化失败，写入null值
            gen.writeNull();
        }
    }
}
