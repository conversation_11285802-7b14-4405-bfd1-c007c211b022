package org.springblade.modules.beachwaste.config;

import com.fasterxml.jackson.databind.module.SimpleModule;
import org.locationtech.jts.geom.Point;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Jackson几何对象序列化配置
 * 配置JTS Point对象的自定义序列化和反序列化器
 * 
 * <AUTHOR>
 */
@Configuration
public class JacksonGeometryConfig {

    /**
     * 配置Jackson ObjectMapper以处理JTS几何对象
     * 注册自定义的Point序列化器和反序列化器
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonGeometryCustomizer() {
        return builder -> {
            SimpleModule geometryModule = new SimpleModule("GeometryModule");
            geometryModule.addSerializer(Point.class, new PointSerializer());
            geometryModule.addDeserializer(Point.class, new PointDeserializer());
            builder.modules(geometryModule);
        };
    }
}
