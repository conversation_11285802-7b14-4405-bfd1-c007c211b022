package org.springblade.modules.beachwaste.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;

import java.io.IOException;

/**
 * JTS Point对象的自定义Jackson反序列化器
 * 从包含经纬度的JSON对象反序列化为Point对象
 * 
 * <AUTHOR>
 */
public class PointDeserializer extends JsonDeserializer<Point> {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory(new PrecisionModel(), 4326);

    @Override
    public Point deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        
        if (node == null || node.isNull()) {
            return null;
        }

        try {
            // 从JSON对象中提取经纬度
            JsonNode lonNode = node.get("longitude");
            JsonNode latNode = node.get("latitude");
            
            if (lonNode == null || latNode == null) {
                return null;
            }

            double longitude = lonNode.asDouble();
            double latitude = latNode.asDouble();
            
            // 创建Point对象
            Point point = GEOMETRY_FACTORY.createPoint(new Coordinate(longitude, latitude));
            
            // 如果有SRID信息，设置它
            JsonNode sridNode = node.get("srid");
            if (sridNode != null) {
                point.setSRID(sridNode.asInt());
            }
            
            return point;
            
        } catch (Exception e) {
            // 如果反序列化失败，返回默认坐标点
            return GEOMETRY_FACTORY.createPoint(new Coordinate(0.0, 0.0));
        }
    }
}
