package org.springblade.modules.beachwaste.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Pattern;

/**
 * 地图事件查询DTO
 * 用于接收地图事件点数据的查询参数
 * 
 * <AUTHOR>
@Data
@Schema(description = "地图事件查询参数")
public class EventMapQueryDTO {
    
    /**
     * 地图可视范围边界框，格式为 minLng,minLat,maxLng,maxLat
     * 用于服务端空间过滤，提升性能
     */
    @Schema(description = "地图边界框，格式：minLng,minLat,maxLng,maxLat", example = "120.7,31.3,120.8,31.4")
    @Pattern(regexp = "^-?\\d+(\\.\\d+)?,-?\\d+(\\.\\d+)?,-?\\d+(\\.\\d+)?,-?\\d+(\\.\\d+)?$", 
             message = "bbox格式错误，应为：minLng,minLat,maxLng,maxLat")
    private String bbox;
    
    /**
     * 事件发现的开始时间，格式 YYYY-MM-DD HH:mm:ss
     */
    @Schema(description = "开始时间", example = "2023-10-01 00:00:00")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", 
             message = "开始时间格式错误，应为：YYYY-MM-DD HH:mm:ss")
    private String startDate;
    
    /**
     * 事件发现的结束时间，格式 YYYY-MM-DD HH:mm:ss
     */
    @Schema(description = "结束时间", example = "2023-10-27 23:59:59")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", 
             message = "结束时间格式错误，应为：YYYY-MM-DD HH:mm:ss")
    private String endDate;
    
    /**
     * 按事件处理状态筛选
     */
    @Schema(description = "事件状态ID", example = "1")
    private Long eventStatus;
    
    /**
     * 按垃圾材质类型筛选
     */
    @Schema(description = "垃圾材质类型ID", example = "2")
    private Long wasteMaterial;
    
    /**
     * 按发现方式筛选 (0-AI/1-人工)
     */
    @Schema(description = "发现方式 (0-AI/1-人工)", example = "0")
    private Integer discoveryMethod;
    
    /**
     * 按网格ID筛选
     */
    @Schema(description = "网格ID", example = "1")
    private Long gridId;
    
    /**
     * 按处理人员ID筛选
     */
    @Schema(description = "处理人员ID", example = "1")
    private Long handlerStaffId;
}