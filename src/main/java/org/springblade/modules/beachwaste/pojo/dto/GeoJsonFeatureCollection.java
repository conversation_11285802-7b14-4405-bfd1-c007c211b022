package org.springblade.modules.beachwaste.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * GeoJSON FeatureCollection DTO
 * 用于地图事件点数据的GeoJSON格式响应
 * 
 * <AUTHOR>
@Data
@Schema(description = "GeoJSON FeatureCollection格式的事件点数据")
public class GeoJsonFeatureCollection {
    
    /**
     * GeoJSON类型，固定为"FeatureCollection"
     */
    @Schema(description = "GeoJSON类型", example = "FeatureCollection")
    private final String type = "FeatureCollection";
    
    /**
     * Feature对象列表
     */
    @Schema(description = "Feature对象列表")
    private List<GeoJsonFeature> features;
    
    /**
     * 构造函数
     */
    public GeoJsonFeatureCollection() {
    }
    
    /**
     * 构造函数
     * 
     * @param features Feature对象列表
     */
    public GeoJsonFeatureCollection(List<GeoJsonFeature> features) {
        this.features = features;
    }
}