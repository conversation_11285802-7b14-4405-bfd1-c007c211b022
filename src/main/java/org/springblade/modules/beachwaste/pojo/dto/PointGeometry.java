package org.springblade.modules.beachwaste.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * GeoJSON Point Geometry DTO
 * 表示点几何信息
 * 
 * <AUTHOR>
@Data
@Schema(description = "GeoJSON Point几何对象")
public class PointGeometry {
    
    /**
     * 几何类型，固定为"Point"
     */
    @Schema(description = "几何类型", example = "Point")
    private final String type = "Point";
    
    /**
     * 坐标数组 [经度, 纬度]
     */
    @Schema(description = "坐标数组 [经度, 纬度]", example = "[121.4737, 31.2304]")
    private double[] coordinates;
    
    /**
     * 构造函数
     */
    public PointGeometry() {
    }
    
    /**
     * 构造函数
     * 
     * @param coordinates 坐标数组 [经度, 纬度]
     */
    public PointGeometry(double[] coordinates) {
        this.coordinates = coordinates;
    }
    
    /**
     * 构造函数
     * 
     * @param longitude 经度
     * @param latitude 纬度
     */
    public PointGeometry(double longitude, double latitude) {
        this.coordinates = new double[]{longitude, latitude};
    }
}