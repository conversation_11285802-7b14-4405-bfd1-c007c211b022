package org.springblade.modules.beachwaste.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.locationtech.jts.geom.Point;
import org.springblade.core.mp.base.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 事件信息表
 * <AUTHOR>
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("event")
public class Event extends BaseEntity {

    /**
     * 事件唯一标识符
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 事件发现时间（UTC+8）
     */
    private Date discoveryTime;

    /**
     * 垃圾材质类型（对应业务层枚举）
	 * @link org.springblade.modules.beachwaste.enums.WasteMaterialEnum
     */
    private Long wasteMaterial;

    /**
     * 垃圾尺寸分类（对应业务层枚举）
	 * @link org.springblade.modules.beachwaste.enums.WasteSizeEnum
     */
    private Long wasteSize;

    /**
     * 发现网格id
     */
    private Long gridId;

    /**
     * 地理坐标（经度,纬度），采用 WGS84 坐标系
     * 使用JsonIgnore避免Jackson序列化时的循环引用问题
     */
    @JsonIgnore
    private Point location;

	/**
	 * 事件发现凭证
	 */
	private String discoveryImagePath;

    /**
     * 发现方式 枚举类（0-AI/1-人工）
	 * @link org.springblade.modules.beachwaste.enums.DiscoveryMethodEnum
     */
    private Long discoveryMethod;

    /**
     * AI 识别置信度值
     */
    private BigDecimal confidence;

    /**
     * 当前处理状态 ID（对应业务层枚举）
	 * @link org.springblade.modules.beachwaste.enums.EventStatusEnum
     */
    private Long eventStatus;

    /**
     * 处理完成上报时间
     */
    private Date handlerReportTime;

    /**
     * 处理后现场照片存储路径
     */
    private String processedImagePath;

    /**
     * 处理人员id
     */
    private Long handlerStaffId;

	/**
	 * 框选图片垃圾位置
	 */
	private String box;

    /**
     * 获取可序列化的位置信息
     * 返回包含经纬度的Map对象，用于JSON序列化
     *
     * @return 包含longitude和latitude的Map，如果location为null则返回null
     */
    @JsonProperty("locationInfo")
    public Map<String, Object> getLocationInfo() {
        if (location == null || location.isEmpty()) {
            return null;
        }

        try {
            Map<String, Object> locationInfo = new HashMap<>();
            locationInfo.put("longitude", location.getX());
            locationInfo.put("latitude", location.getY());
            if (location.getSRID() != 0) {
                locationInfo.put("srid", location.getSRID());
            }
            return locationInfo;
        } catch (Exception e) {
            return null;
        }
    }

}
