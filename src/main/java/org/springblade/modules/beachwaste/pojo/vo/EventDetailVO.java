package org.springblade.modules.beachwaste.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 事件详情VO
 * <AUTHOR>
 */
@Data
public class EventDetailVO {

	/**
	 * 事件标题
	 */
	private String eventTitle;

    /**
     * 事件编号
     */
    private Long id;

    /**
     * 垃圾尺寸分类
     */
    private String wasteSizeDesc;

    /**
     * 垃圾材质分类
     */
    private String wasteMaterialDesc;

    /**
     * 发现网格
     */
    private String gridName;

    /**
     * 发现形式
     */
    private String discoveryMethodDesc;

    /**
     * 置信度
     */
    private BigDecimal confidence;

	/**
	 * 当前处理状态 ID（对应业务层枚举）
	 * @link org.springblade.modules.beachwaste.enums.EventStatusEnum
	 */
	private Long eventStatus;

    /**
     * 发现时间（精确到十分秒）
     */
    private Date discoveryTime;

    /**
     * 发现位置（经度,纬度）
     */
    private String location;

	/**
	 * 事件发现凭证
	 */
	private String discoveryImagePath;

    /**
     * 发现凭证（图片路径）
     */
    private String imagePath;

    /**
     * 处理网格员
     */
    private String handlerStaffName;

    /**
     * 处理上报时间
     */
    private Date handlerReportTime;

    /**
     * 已处理现场照片
     */
    private String processedImagePath;

	/**
	 * 框选图片垃圾位置
	 */
	private String box;

}
