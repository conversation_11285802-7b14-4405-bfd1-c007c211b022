package org.springblade.modules.beachwaste.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * GeoJSON Feature DTO
 * 表示单个地理要素，包含几何信息和属性信息
 * 
 * <AUTHOR>
@Data
@Schema(description = "GeoJSON Feature对象")
public class GeoJsonFeature {
    
    /**
     * GeoJSON类型，固定为"Feature"
     */
    @Schema(description = "GeoJSON类型", example = "Feature")
    private final String type = "Feature";
    
    /**
     * 几何信息
     */
    @Schema(description = "几何信息")
    private PointGeometry geometry;
    
    /**
     * 属性信息
     */
    @Schema(description = "属性信息")
    private Map<String, Object> properties;
    
    /**
     * 构造函数
     */
    public GeoJsonFeature() {
    }
    
    /**
     * 构造函数
     * 
     * @param geometry 几何信息
     * @param properties 属性信息
     */
    public GeoJsonFeature(PointGeometry geometry, Map<String, Object> properties) {
        this.geometry = geometry;
        this.properties = properties;
    }
}