package org.springblade.modules.fh.component.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.fh.enums.FlightTaskStatus;
import org.springblade.modules.fh.mapper.FhFlightTaskMapper;
import org.springblade.modules.fh.pojo.entity.FhFlightTask;
import org.springblade.modules.fh.pojo.vo.FhFlightTaskTrackVO;
import org.springblade.modules.fh.service.IFhFlightTaskService;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 飞行任务距离定时更新
 * 定时查询飞行任务表中的数据，当任务状态为成功且飞行距离为空时，
 * 调用飞行任务轨迹查询的相关方法，把获取到对应的飞行距离数据更新到数据库中
 *
 * <AUTHOR> AI
 */
@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class FlightTaskDistanceScheduler {

    private final FhFlightTaskMapper fhFlightTaskMapper;
    private final IFhFlightTaskService fhFlightTaskService;

    /**
     * 每5分钟执行一次，检查并更新飞行任务的飞行距离
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void updateFlightTaskDistance() {
        log.info("开始执行飞行任务距离更新定时任务");

        try {
            // 查询任务状态为成功且飞行距离为空的飞行任务
            List<FhFlightTask> tasksToUpdate = findTasksWithoutDistance();

            if (tasksToUpdate.isEmpty()) {
                log.info("没有需要更新飞行距离的飞行任务");
                return;
            }

            log.info("找到{}个需要更新飞行距离的飞行任务", tasksToUpdate.size());
            updateTasksDistance(tasksToUpdate);
            log.info("飞行任务距离更新定时任务执行完成");
        } catch (Exception e) {
            log.error("执行飞行任务距离更新定时任务出错", e);
        }
    }

    /**
     * 查询任务状态为成功且飞行距离为空的飞行任务
     */
    private List<FhFlightTask> findTasksWithoutDistance() {
        return fhFlightTaskMapper.selectList(
            new LambdaQueryWrapper<FhFlightTask>()
                .eq(FhFlightTask::getTaskStatus, FlightTaskStatus.SUCCESS)
                .isNull(FhFlightTask::getFlightDistance)
        );
    }

    /**
     * 更新飞行任务的飞行距离
     * 通过调用飞行任务轨迹查询方法获取飞行距离，并更新到数据库
     */
    private void updateTasksDistance(List<FhFlightTask> tasksToUpdate) {
        if (tasksToUpdate == null || tasksToUpdate.isEmpty()) {
            return;
        }

        for (FhFlightTask task : tasksToUpdate) {
            try {
                // 调用飞行任务轨迹查询方法获取飞行距离
                FhFlightTaskTrackVO trackVO = fhFlightTaskService.getFlightTaskTrack(task.getUuid());

                if (trackVO != null && trackVO.getTrack() != null) {
                    // 获取飞行距离
                    Integer flightDistance = trackVO.getTrack().getFlightDistance();

                    if (flightDistance != null && flightDistance > 0) {
                        // 更新飞行任务的飞行距离
                        task.setFlightDistance(flightDistance.longValue());

                        // 更新数据库
                        int result = fhFlightTaskMapper.updateById(task);
                        if (result > 0) {
                            log.info("成功更新飞行任务[{}]的飞行距离为{}米", task.getUuid(), flightDistance);
                        } else {
                            log.warn("更新飞行任务[{}]的飞行距离失败", task.getUuid());
                        }
                    } else {
                        log.warn("飞行任务[{}]的飞行距离为空或无效", task.getUuid());
                    }
                } else {
                    log.warn("获取飞行任务[{}]的轨迹信息失败", task.getUuid());
                }
            } catch (Exception e) {
                log.error("更新飞行任务[{}]的飞行距离时发生异常", task.getUuid(), e);
            }
        }
    }
}
