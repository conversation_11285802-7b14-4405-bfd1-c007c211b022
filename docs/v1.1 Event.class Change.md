好的，明白了！如果您使用的是 MyBatis 而非 Hibernate/JPA，那么整个适配过程的思路是不同的，它不依赖于注解，而是依赖于**自定义的 `TypeHandler`**。

`TypeHandler` 是 MyBatis 的一个核心组件，它的作用是**在 Java 类型和 JDBC 类型之间建立一座桥梁**。我们需要创建一个 `TypeHandler` 来告诉 MyBatis 如何将数据库返回的 `org.postgresql.util.PGobject`（PostGIS 的 `geometry` 类型在 JDBC 层通常表现为此类型）转换成我们想要的 Java `Point` 对象，反之亦然。

以下是详细的步骤。

---

### 第一步：添加 Maven/Gradle 依赖

虽然不使用 Hibernate，但我们仍然需要 JTS 库来在 Java 代码中使用 `Point` 等几何对象，以及一个 PostGIS 的 JDBC 辅助库来解析数据。

#### `pom.xml` (Maven):

```xml
<!-- JTS Core: 提供了 Point, Polygon 等几何对象 -->
<dependency>
    <groupId>org.locationtech.jts</groupId>
    <artifactId>jts-core</artifactId>
    <version>1.19.0</version>
</dependency>

<!-- PostGIS JDBC: 提供了 WKT/WKB 解析器 -->
<dependency>
    <groupId>net.postgis</groupId>
    <artifactId>postgis-jdbc</artifactId>
    <version>2.5.1</version> <!-- 这个版本比较稳定，可以根据需要调整 -->
</transitive>

<!-- 您项目中已有的 MyBatis 和 PostgreSQL JDBC 驱动依赖 -->
<!-- <dependency>
    <groupId>org.mybatis.spring.boot</groupId>
    <artifactId>mybatis-spring-boot-starter</artifactId>
    ...
</dependency>
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
    ...
</dependency> -->
```

请确保添加了 `jts-core` 和 `postgis-jdbc`。

---

### 第二步：修改您的实体类 (`Event.java`)

实体类现在只是一个简单的 POJO（Plain Old Java Object），不需要任何注解。

```java
import org.locationtech.jts.geom.Point; // 导入JTS的Point类

public class Event {

    private Long id;
    
    // ... 其他字段
    
    private Point location; // 字段类型从 String 变为 Point

    // --- Getters and Setters ---
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Point getLocation() {
        return location;
    }

    public void setLocation(Point location) {
        this.location = location;
    }

    // ... 其他字段的 Getters 和 Setters
}
```

---

### 第三步：【核心】创建自定义的 `PointTypeHandler`

这是最关键的一步。创建一个新的 Java 类，实现 MyBatis 的 `TypeHandler` 接口。

```java
package com.yourcompany.project.typehandler; // 包名请自行修改

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;
import org.postgis.PGgeometry;
import org.postgis.jts.JtsWrapper;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * MyBatis TypeHandler for JTS Point <-> PostGIS GEOMETRY
 */
@MappedJdbcTypes(JdbcType.OTHER) // 告诉MyBatis这个Handler处理所有未知的JDBC类型
@MappedTypes(Point.class)       // 告诉MyBatis这个Handler处理Java的Point类型
public class PointTypeHandler extends BaseTypeHandler<Point> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Point parameter, JdbcType jdbcType) throws SQLException {
        PGgeometry geometry = new PGgeometry(JtsWrapper.toLinearRing(parameter));
        ps.setObject(i, geometry);
    }

    @Override
    public Point getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object obj = rs.getObject(columnName);
        if (obj == null) {
            return null;
        }
        return toPoint(obj);
    }

    @Override
    public Point getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object obj = rs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        return toPoint(obj);
    }

    @Override
    public Point getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object obj = cs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        return toPoint(obj);
    }

    private Point toPoint(Object obj) throws SQLException {
        if (obj instanceof PGgeometry) {
            Geometry geometry = ((PGgeometry) obj).getGeometry();
            if (geometry instanceof Point) {
                return (Point) geometry;
            }
        }
        // 可以添加对其他可能的返回类型的处理，例如 WKT 字符串
        return null;
    }
}
```

---

### 第四步：在 MyBatis 配置中注册 `TypeHandler`

您需要告诉 MyBatis 存在这个自定义的 `TypeHandler`，并让它在全局生效。

#### 如果您使用 XML 配置 (`mybatis-config.xml`):

在 `<typeHandlers>` 标签中添加一行。

```xml
<configuration>
    <!-- ...其他配置... -->
    <typeHandlers>
        <typeHandler handler="com.yourcompany.project.typehandler.PointTypeHandler"/>
    </typeHandlers>
    <!-- ...其他配置... -->
</configuration>
```

#### 如果您使用 Spring Boot 和 `application.properties` 或 `application.yml`:

在配置文件中指定 `TypeHandler` 所在的包，MyBatis 会自动扫描并注册。

**`application.properties`:**
```properties
# 指定TypeHandler所在的包路径，MyBatis会自动扫描
mybatis.type-handlers-package=com.yourcompany.project.typehandler
```

**`application.yml`:**
```yaml
mybatis:
  type-handlers-package: com.yourcompany.project.typehandler
```

---

### 第五步：调整 Mapper XML 文件

现在，您的 Mapper XML 文件可以无缝地使用 `Point` 类型了。

**查询示例 (`EventMapper.xml`)**

```xml
<select id="findById" resultType="com.yourcompany.project.entity.Event">
    SELECT 
        id, 
        discovery_time, 
        <!-- 其他字段 -->
        location  <!-- 直接查询 location 列 -->
    FROM 
        event
    WHERE 
        id = #{id}
</select>
```
*   **查询时**：MyBatis 从数据库获取到 `location` 列的值，发现它是 `OTHER` JDBC 类型，于是查找能处理此类型的 `TypeHandler`。它找到了我们的 `PointTypeHandler`，并调用其 `getNullableResult` 方法，将数据库对象转换成 Java `Point` 对象，然后设置到 `Event` 实体的 `location` 字段中。

**插入/更新示例**

```xml
<insert id="insertEvent" parameterType="com.yourcompany.project.entity.Event">
    INSERT INTO event (location, /* 其他字段 */)
    VALUES (#{location, jdbcType=OTHER}, /* 其他值 */)
</insert>
```
*   **插入时**：MyBatis 拿到 `Event` 对象中的 `location` 字段（它是一个 `Point` 对象），发现它的类型是 `Point.class`。它找到了能处理此类型的 `PointTypeHandler`，并调用其 `setNonNullParameter` 方法，将 `Point` 对象转换成 PostGIS JDBC 驱动能理解的 `PGgeometry` 对象，然后通过 `PreparedStatement` 设置到 SQL 语句中。
*   `jdbcType=OTHER` 是一个好习惯，明确告诉 MyBatis 这个参数的 JDBC 类型。

完成以上步骤后，您的 MyBatis 应用就能像使用普通类型一样，顺滑地处理 PostGIS 的地理空间数据了。