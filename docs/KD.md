
### **产品需求文档 (PRD) - 地图事件点数据 API 接口**

| 文档版本 | V1.1 |
| :--- | :--- |
| **项目名称** | 地图可视化项目 - 事件聚类图数据支持 |
| **需求概述** | 基于 `event` 表，开发一个后端数据接口。该接口需返回符合 GeoJSON 规范的事件点数据，以供前端 Mapmost SDK 进行聚类展示。 |



### 1. 功能背景与目标

**1.1. 背景分析**

前端将使用 Mapmost SDK 实现地图聚类功能，用于可视化展示系统中记录的各类“事件”（如垃圾发现、设备告警等）。数据源为后端的 `event` 表。前端的聚类功能是在浏览器端完成的，因此后端的核心任务是**提供原始的、未聚合的事件点位数据**。

**1.2. 核心目标**

开发一个高效、稳定的 Java 后端 API 接口，该接口能够：
1.  根据查询条件，从 `event` 表中检索事件数据。
2.  将查询结果封装成标准的 **GeoJSON FeatureCollection** 格式。
3.  响应前端请求，为其提供渲染地图所需的全量或筛选后的事件点数据。

### 2. 功能需求 (Functional Requirements)

#### 2.1. API 接口定义

| 项目 | 描述 |
| :--- | :--- |
| **接口名称** | 获取地图事件点位数据 |
| **接口路径** | `/api/v1/events` (建议使用复数名词) |
| **HTTP 方法** | `GET` |
| **认证要求** | 根据项目安全策略决定 (如: 需要 JWT Token) |

#### 2.2. 请求参数

| 参数名 | 类型 | 是否必填 | 对应数据库字段 | 描述 | 示例 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `bbox` | String | 否 | `location` | **(性能优化)** 地图可视范围边界框，格式为 `minLng,minLat,maxLng,maxLat`。用于服务端空间过滤。| `120.7,31.3,120.8,31.4` |
| `startDate` | String | 否 | `discovery_time` | 事件发现的开始时间，格式 `YYYY-MM-DD HH:mm:ss`。 | `2023-10-01 00:00:00` |
| `endDate` | String | 否 | `discovery_time` | 事件发现的结束时间，格式 `YYYY-MM-DD HH:mm:ss`。 | `2023-10-27 23:59:59` |
| `eventStatus` | Long | 否 | `event_status` | 按事件处理状态筛选。 | `1` |
| `wasteMaterial`| Long | 否 | `waste_material` | 按垃圾材质类型筛选。 | `2` |
| `discoveryMethod`| Integer | 否 | `discovery_method`| 按发现方式筛选 (0-AI/1-人工)。 | `0` |

**默认查询行为:**
*   接口应**默认过滤**掉已删除的记录，即查询条件中必须包含 `is_deleted = 0`。
*   根据业务需要，可能还需默认过滤某些 `status` 值，例如 `status = 1`。

#### 2.3. 成功响应 (Success Response)

*   **HTTP 状态码**: `200 OK`
*   **Content-Type**: `application/json;charset=utf-8`
*   **响应体格式**: **GeoJSON FeatureCollection**

**响应体结构详解:**

```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": {
        "type": "Point",
        "coordinates": [121.4737, 31.2304] // 从 location 字段解析出的 [经度, 纬度]
      },
      "properties": {
        "id": 1001,
        "discoveryTime": "2023-10-26T10:30:00Z",
        "wasteMaterial": 1,
        "wasteSize": 2,
        "eventStatus": 1,
        "confidence": 95.50,
        "discoveryImagePath": "/images/discovery/abc.jpg"
      }
    }
    // ... 更多 Feature 对象
  ]
}
```

**`properties` 字段映射说明:**
`properties` 对象用于存放每个点的业务属性，供前端在点击、悬浮时展示。建议至少包含以下字段：

| `properties` 键名 | 对应 `event` 表字段 | 数据类型 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | `id` | Long | 事件唯一ID |
| `discoveryTime` | `discovery_time` | String (ISO 8601) | 发现时间 |
| `wasteMaterial` | `waste_material` | Long | 垃圾材质类型ID |
| `wasteSize` | `waste_size` | Long | 垃圾尺寸分类ID |
| `eventStatus` | `event_status` | Long | 事件状态ID |
| `confidence` | `confidence` | Number | AI置信度 |
| `discoveryImagePath` | `discovery_image_path` | String | 发现图片路径 |

#### 2.4. 异常响应 (Error Response)

| HTTP 状态码 | 触发条件 | 响应体示例 |
| :--- | :--- | :--- |
| `400 Bad Request` | 请求参数格式错误，如`bbox`或日期格式不正确。 | `{"code": 400, "message": "Invalid date format for startDate."}` |
| `500 Internal Server Error`| 服务器内部错误，如数据库查询失败。 | `{"code": 500, "message": "Internal server error."}` |

### 3. Java 后端实现指南

#### 3.1. **【重要】数据库 Schema 调整建议**

当前 `event` 表中 `location` 字段类型为 `varchar(255)`，但备注中描述其意图是存储 PostGIS 的 `geometry` 类型。

**强烈建议进行以下调整，否则将导致严重的性能问题和复杂的查询逻辑！**

1.  **修改字段类型**：将 `location` 字段的类型更改为 `GEOMETRY(Point, 4326)`。
    ```sql
    -- 步骤1: 添加一个临时的 geometry 列
    ALTER TABLE public.event ADD COLUMN geom GEOMETRY(Point, 4326);

    -- 步骤2: 从 varchar 更新数据到新列 (假设格式是 '经度,纬度')
    UPDATE public.event 
    SET geom = ST_SetSRID(ST_MakePoint(
        CAST(split_part(location, ',', 1) AS DOUBLE PRECISION), 
        CAST(split_part(location, ',', 2) AS DOUBLE PRECISION)
    ), 4326)
    WHERE location IS NOT NULL AND location LIKE '%,%';

    -- 步骤3: 删除旧列
    ALTER TABLE public.event DROP COLUMN location;

    -- 步骤4: 重命名新列
    ALTER TABLE public.event RENAME COLUMN geom TO location;
    ```
2.  **创建空间索引**：为新的 `location` 列创建 GIST 索引，这是实现高效 `bbox` 查询的关键。
    ```sql
    CREATE INDEX idx_event_location ON public.event USING GIST(location);
    ```

#### 3.2. 技术栈与依赖

*   **框架**: Spring Boot
*   **数据库交互**:
    *   **JPA/Hibernate**: 需要引入 `hibernate-spatial` 依赖来处理 `geometry` 类型。
    *   **MyBatis**: 同样支持，需要自定义 TypeHandler。
*   **GeoJSON 库**: `org.wololo:jts2geojson` 或手动构建 DTO。

#### 3.3. 核心业务逻辑实现 (以 JPA 和调整后的 Schema 为例)

1.  **Entity (`Event.java`)**:
    *   使用 `@Type(type = "jts_geometry")` 注解来映射 `location` 字段。
    ```java
    import org.locationtech.jts.geom.Point;
    import org.hibernate.annotations.Type;
    // ...
    
    @Entity
    @Table(name = "event")
    public class Event {
        // ... 其他字段
        
        @Column(name = "location", columnDefinition = "geometry(Point,4326)")
        @Type(type = "jts_geometry")
        private Point location;
    }
    ```

2.  **Repository (`EventRepository.java`)**:
    *   使用 JPA Criteria API 或 `@Query` 注解构建动态查询。
    ```java
    public interface EventRepository extends JpaRepository<Event, Long>, JpaSpecificationExecutor<Event> {
        // JpaSpecificationExecutor 允许我们使用 Specification 来构建动态查询
    }
    ```

3.  **Service (`EventService.java`)**:
    *   构建 `Specification` 来组合所有查询条件。
    ```java
    public List<Event> findEvents(String bbox, String startDate, Long eventStatus, ...) {
        Specification<Event> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 默认条件
            predicates.add(cb.equal(root.get("isDeleted"), 0));

            // Bbox 筛选 (核心空间查询)
            if (bbox != null && !bbox.isEmpty()) {
                // 解析 bbox: "minLng,minLat,maxLng,maxLat"
                String[] coords = bbox.split(",");
                GeometryFactory geomFactory = new GeometryFactory();
                Coordinate[] polyCoords = new Coordinate[] {
                    new Coordinate(Double.parseDouble(coords[0]), Double.parseDouble(coords[1])),
                    new Coordinate(Double.parseDouble(coords[0]), Double.parseDouble(coords[3])),
                    new Coordinate(Double.parseDouble(coords[2]), Double.parseDouble(coords[3])),
                    new Coordinate(Double.parseDouble(coords[2]), Double.parseDouble(coords[1])),
                    new Coordinate(Double.parseDouble(coords[0]), Double.parseDouble(coords[1]))
                };
                Polygon boundingBox = geomFactory.createPolygon(polyCoords);
                // 使用 PostGIS 的 ST_Intersects 函数
                predicates.add(cb.isTrue(cb.function("ST_Intersects", Boolean.class, root.get("location"), cb.literal(boundingBox))));
            }

            // 其他条件...
            if (eventStatus != null) {
                predicates.add(cb.equal(root.get("eventStatus"), eventStatus));
            }
            if (startDate != null) {
                // 解析并添加时间范围谓词
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return eventRepository.findAll(spec);
    }
    ```

4.  **Controller (`EventController.java`)**:
    *   接收请求，调用 Service，然后将返回的 `List<Event>` 转换为 `GeoJsonFeatureCollection` DTO
        **DTO/POJO 定义**:
    **`GeoJsonFeatureCollection.java`**
    ```java
    public class GeoJsonFeatureCollection {
        private final String type = "FeatureCollection";
        private List<GeoJsonFeature> features;
        // Getters, Setters, Constructors
    }
    ```

    **`GeoJsonFeature.java`**
    ```java
    public class GeoJsonFeature {
        private final String type = "Feature";
        private PointGeometry geometry;
        private Map<String, Object> properties;
        // Getters, Setters, Constructors
    }
    ```

    **`PointGeometry.java`**
    ```java
    public class PointGeometry {
        private final String type = "Point";
        private double[] coordinates; // [longitude, latitude]
        // Getters, Setters, Constructors
    }
    ```
    
    *   在转换时，从 `Event` 实体中取出 `location` (JTS `Point` 对象) 和其他属性，分别填入 GeoJSON 的 `geometry` 和 `properties` 中。

### 4. 非功能性需求

*   **性能**: 在`event`表数据量为 10 万条时，使用 `bbox` 参数的查询响应时间应小于 1 秒。
*   **可扩展性**: 接口和查询逻辑应易于扩展，以支持未来可能增加的筛选条件（如 `grid_id`, `handler_staff_id` 等）。
*   **数据准确性**: 务必保证 `location` 字段中的经纬度顺序正确（经度在前，纬度在后）。